"""
Main Window UI Builder for constructing UI components.
"""

from PyQt6.QtWidgets import QWidget, QHBoxLayout, QVBoxLayout, QLabel
from PyQt6.QtCore import Qt

from ..managers.localization_manager import get_localization_manager, tr
from ..components.drag_and_drop.file_upload import FileUpload
from ..components.progress_bar.translation_progress_bar import TranslationProgressBar
from ..components.logger.log_widget import LogWidget
from ..components.combo_box.source_language_combo_box import SourceLanguageComboBox
from ..components.combo_box.target_language_combo_box import TargetLanguageComboBox
from ..components.combo_box.translator_combo_box import Translator<PERSON>ombo<PERSON>ox
from ..components.input.batch_size_input import BatchSizeInput
from ..components.scroll_area.sheets_scroll_area import SheetsScrollArea
from ..components.button.translate_button import TranslateButton
from ..components.button.cancel_button import CancelButton
from ..components.button.export_button import ExportButton
from ..components.focus_frame.card_frame import CardFrame
from ..components.logger.gui_log_handler import BusinessLogicLogger


class MainWindowUIBuilder:
    """Builder class for constructing main window UI components."""
    
    def __init__(self, main_window):
        """Initialize the UI builder with reference to main window."""
        self.main_window = main_window
        self.localization_manager = get_localization_manager()

        # Store references to labels that need updating
        self.localizable_labels = {}
    
    def setup_central_widget(self):
        """Setup the central widget and main layout."""
        central_widget = QWidget()
        central_widget.setStyleSheet("QWidget { background-color: #0d1117; }")
        self.main_window.setCentralWidget(central_widget)

        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(16, 16, 16, 16)
        main_layout.setSpacing(20)

        left_panel = self.create_left_panel()
        main_layout.addWidget(left_panel, 2)

        right_panel = self.create_right_panel()
        main_layout.addWidget(right_panel, 1)

        self.setup_status_bar()
    
    def setup_status_bar(self):
        """Setup the status bar."""
        status_bar = self.main_window.statusBar()
        status_bar.showMessage(tr("ready_status"))

        plan_b_label = QLabel("PLAN-B")
        plan_b_label.setStyleSheet("""
            QLabel {
                color: #8b949e;
                font-size: 11px;
                padding: 4px 8px;
                background-color: transparent;
            }
        """)
        status_bar.addPermanentWidget(plan_b_label)

        status_bar.setStyleSheet("""
            QStatusBar {
                background-color: #0d1117;
                color: #8b949e;
                border-top: 1px solid #30363d;
                font-size: 11px;
                padding: 4px 8px;
            }
        """)
    
    def create_left_panel(self):
        """Create the left panel with file upload, progress, and logs."""
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(8)

        # Add 8px top margin for left panel
        left_layout.addSpacing(8)

        # File upload area
        self.main_window.file_upload = FileUpload()
        left_layout.addWidget(self.main_window.file_upload)

        # Add 5px spacing before progress card
        left_layout.addSpacing(5)

        # Progress bar card
        progress_card = self.create_progress_card()
        left_layout.addWidget(progress_card)

        # Add 5px spacing before logs card
        left_layout.addSpacing(5)

        # Logs section card
        logs_card = self.create_logs_card()
        left_layout.addWidget(logs_card, 1)  # Give it stretch factor to fill remaining space

        # Add 6px bottom spacing for left panel
        left_layout.addSpacing(6)

        return left_widget
    
    def create_progress_card(self):
        """Create the progress card."""
        progress_card = CardFrame()

        # Progress bar
        self.main_window.progress_bar = TranslationProgressBar()
        progress_card.add_widget(self.main_window.progress_bar)

        return progress_card
    
    def create_logs_card(self):
        """Create the logs card."""
        logs_card = CardFrame(tr("logs_title"), "fieldLabel")

        # Store reference for updating
        self.localizable_labels['logs_title'] = logs_card.title_label

        # Log widget
        self.main_window.log_widget = LogWidget()
        logs_card.add_widget(self.main_window.log_widget)
        
        # Initialize business logic logger with the log widget
        self.main_window.business_logger = BusinessLogicLogger(self.main_window.log_widget)

        # Display prominent welcome message immediately after logger initialization
        self.main_window.business_logger.log_status_message("====== WELCOME TO EXCEL TRANSLATOR ======")

        # Set progress bar to initial state
        if hasattr(self.main_window, 'progress_bar'):
            self.main_window.progress_bar.set_progress(0, tr("progress_title"))

        # Log initialization of only the default/selected translator
        self._log_default_translator_initialization()

        if self.main_window.excel_handler:
            self.main_window.business_logger.log_info("Excel file handler ready")

        return logs_card
    
    def _log_default_translator_initialization(self):
        """Log initialization of only the default/selected translator."""
        if not self.main_window.business_logger:
            return

        # First, check if we have the dropdown widget and use its current selection
        default_translator = None

        if hasattr(self.main_window, 'translator_combo'):
            current_text = self.main_window.translator_combo.currentText()
            if "Google" in current_text:
                default_translator = "google"
            elif "DeepL" in current_text:
                default_translator = "deepl"

        # If no dropdown available, fall back to settings
        if not default_translator:
            default_translator = "deepl"  # Default fallback changed to DeepL
            if self.main_window.settings and hasattr(self.main_window.settings, 'api'):
                default_translator = getattr(self.main_window.settings.api, 'default_translation_engine', 'deepl')

        # Log only the default translator if it's available AND properly configured
        if default_translator.lower() == "google" and self.main_window.google_translator and self.main_window.google_translator.client:
            self.main_window.business_logger.log_api_initialization("Google Translate", True)
        elif default_translator.lower() == "deepl" and self.main_window.deepl_translator and self.main_window.deepl_translator.client:
            self.main_window.business_logger.log_api_initialization("DeepL", True)
        else:
            # Fallback: log the first available translator that's properly configured
            google_available = (self.main_window.google_translator and
                              hasattr(self.main_window.google_translator, 'client') and
                              self.main_window.google_translator.client is not None)
            deepl_available = (self.main_window.deepl_translator and
                             hasattr(self.main_window.deepl_translator, 'client') and
                             self.main_window.deepl_translator.client is not None)

            # Prioritize DeepL over Google in fallback
            if deepl_available:
                self.main_window.business_logger.log_api_initialization("DeepL", True)
            elif google_available:
                self.main_window.business_logger.log_api_initialization("Google Translate", True)
            else:
                # Check if API keys are configured but services failed to initialize
                has_google_key = bool(self.main_window.settings and self.main_window.settings.api.google_api_key)
                has_deepl_key = bool(self.main_window.settings and self.main_window.settings.api.deepl_api_key)

                if (has_google_key or has_deepl_key) and self.main_window.business_logger:
                    self.main_window.business_logger.log_warning("Translation services configured but not properly initialized")
                elif self.main_window.business_logger:
                    self.main_window.business_logger.log_info("No translation API keys configured")
    
    def create_right_panel(self):
        """Create the right panel with controls and settings."""
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(12)

        # Add 6px top margin for right panel
        right_layout.addSpacing(6)

        # Convert section
        convert_section = self.create_convert_section()
        right_layout.addWidget(convert_section)
        
        # Tools section
        tools_section = self.create_tools_section()
        right_layout.addWidget(tools_section)
        
        # Sheets detected section
        sheets_section = self.create_sheets_section()
        right_layout.addWidget(sheets_section)
        
        # Add spacing before buttons
        right_layout.addSpacing(10)

        # Action buttons
        buttons_section = self.create_buttons_section()
        right_layout.addWidget(buttons_section)
        
        return right_widget
    
    def create_convert_section(self):
        """Create the convert section with language selection."""
        convert_frame = CardFrame()
        convert_frame.layout.setContentsMargins(15, 15, 15, 25)  # Added bottom margin instead of title
        convert_frame.layout.setSpacing(5)

        # Source language section
        source_label = QLabel(tr("source_language"))
        source_label.setObjectName("fieldLabel")
        convert_frame.add_widget(source_label)

        # Store reference for updating
        self.localizable_labels['source_language'] = source_label

        self.main_window.source_language = SourceLanguageComboBox()
        convert_frame.add_widget(self.main_window.source_language)

        # Add some space between language sections
        convert_frame.layout.addSpacing(5)

        # Target language section
        target_label = QLabel(tr("target_language"))
        target_label.setObjectName("fieldLabel")
        convert_frame.add_widget(target_label)

        # Store reference for updating
        self.localizable_labels['target_language'] = target_label

        self.main_window.target_language = TargetLanguageComboBox()
        convert_frame.add_widget(self.main_window.target_language)

        return convert_frame
    
    def create_tools_section(self):
        """Create the tools section with translator and batch size."""
        tools_frame = CardFrame()
        tools_frame.layout.setContentsMargins(15, 15, 15, 25)  # Added bottom margin instead of title
        tools_frame.layout.setSpacing(5)

        # Translator label
        translator_label = QLabel(tr("translator_selection"))
        translator_label.setObjectName("fieldLabel")
        tools_frame.add_widget(translator_label)

        # Store reference for updating
        self.localizable_labels['translator_selection'] = translator_label

        # Translator dropdown
        self.main_window.translator_combo = TranslatorComboBox()
        tools_frame.add_widget(self.main_window.translator_combo)

        # Add some space between translator and batch size sections
        tools_frame.layout.addSpacing(5)

        # Batch size label
        batch_label = QLabel(tr("batch_size"))
        batch_label.setObjectName("fieldLabel")
        tools_frame.add_widget(batch_label)

        # Store reference for updating
        self.localizable_labels['batch_size'] = batch_label

        # Batch size input
        self.main_window.batch_size_input = BatchSizeInput()
        tools_frame.add_widget(self.main_window.batch_size_input)

        return tools_frame
    
    def create_sheets_section(self):
        """Create the sheets section with scroll area."""
        sheets_frame = CardFrame()
        sheets_frame.layout.setContentsMargins(15, 15, 15, 15)
        sheets_frame.layout.setSpacing(5)

        # Field label (same style as other field labels)
        title_label = QLabel(tr("sheets_detected"))
        title_label.setObjectName("fieldLabel")
        sheets_frame.add_widget(title_label)

        # Store reference for updating
        self.localizable_labels['sheets_detected'] = title_label

        # Sheets scroll area
        self.main_window.sheets_scroll = SheetsScrollArea()
        sheets_frame.add_widget(self.main_window.sheets_scroll)

        return sheets_frame
    
    def create_buttons_section(self):
        """Create the buttons section with action buttons."""
        buttons_widget = QWidget()
        buttons_layout = QVBoxLayout(buttons_widget)
        buttons_layout.setContentsMargins(0, 0, 0, 0)
        
        # Translate and Cancel buttons (horizontal)
        action_layout = QHBoxLayout()
        
        self.main_window.translate_button = TranslateButton()
        self.main_window.cancel_button = CancelButton()
        
        action_layout.addWidget(self.main_window.translate_button)
        action_layout.addWidget(self.main_window.cancel_button)
        
        buttons_layout.addLayout(action_layout)

        # Export button
        self.main_window.export_button = ExportButton()
        buttons_layout.addWidget(self.main_window.export_button)

        # Add 9px bottom spacing for right panel
        buttons_layout.addSpacing(9)

        return buttons_widget

    def update_ui_texts(self):
        """Update all UI texts when language changes."""
        # Update status bar
        if hasattr(self.main_window, 'statusBar'):
            self.main_window.statusBar().showMessage(tr("ready_status"))

        # Update all stored localizable labels
        for key, label in self.localizable_labels.items():
            if label and hasattr(label, 'setText'):
                label.setText(tr(key))

        # Update progress bar if it's showing the initial progress title
        if hasattr(self.main_window, 'progress_bar') and self.main_window.progress_bar:
            current_text = self.main_window.progress_bar.status_label.text() if hasattr(self.main_window.progress_bar, 'status_label') else ""
            # Check if it's showing the progress title in any language
            if ("Translation Progress" in current_text or
                "翻訳進捗" in current_text or
                "Tiến trình Dịch" in current_text or
                current_text.strip() == ""):
                self.main_window.progress_bar.set_progress(0, tr("progress_title"))
